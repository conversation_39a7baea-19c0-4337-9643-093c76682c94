'use client'

import { useEffect } from 'react'
import { useMutation } from '@apollo/client'
import { CREATE_MAINTENANCE_CATEGORY } from '@/app/lib/graphQL/mutation'
import {
    GET_MAINTENANCE_CATEGORY,
    GetMaintenanceCategories,
} from '@/app/lib/graphQL/query'
import { useRouter } from 'next/navigation'

import { preventCrewAccess } from '@/app/helpers/userHelper'
import { Input } from '@/components/ui/input'
import { FooterWrapper } from '@/components/footer-wrapper'
import SeaLogsButton from '@/components/ui/sea-logs-button'
import {
    <PERSON><PERSON>,
    Card,
    CardContent,
    CardHeader,
    CardTitle,
    H1,
    H3,
    Label,
} from '@/components/ui'

export default function NewMaintenanceCategory() {
    const router = useRouter()

    const handleCreate = async () => {
        const categoryName = (
            document.getElementById('category-name') as HTMLInputElement
        )?.value
        const categoryAbbr = (
            document.getElementById('category-abbr') as HTMLInputElement
        )?.value
        const variables = {
            input: {
                name: categoryName,
                abbreviation: categoryAbbr,
            },
        }
        if (categoryName !== '') {
            return await mutationcreateMaintenanceCategory({
                variables,
            })
        }
    }

    const [
        mutationcreateMaintenanceCategory,
        { loading: mutationcreateMaintenanceCategoryLoading },
    ] = useMutation(CREATE_MAINTENANCE_CATEGORY, {
        refetchQueries: () => [
            {
                query: GET_MAINTENANCE_CATEGORY,
                variables: {
                    clientID: +(localStorage.getItem('clientId') ?? 0),
                },
            },
            {
                query: GetMaintenanceCategories,
                variables: {
                    clientID: +(localStorage.getItem('clientId') ?? 0),
                },
            },
        ],
        awaitRefetchQueries: true,
        onCompleted: (response: any) => {
            const data = response.createMaintenanceCategory
            if (data.id > 0) {
                // router.push('/settings/maintenance/category')
                router.back()
            } else {
                console.error(
                    'mutationcreateMaintenanceCategory error',
                    response,
                )
            }
        },
        onError: (error: any) => {
            console.error('mutationcreateMaintenanceCategory error', error)
        },
    })

    useEffect(() => {
        preventCrewAccess()
    }, [])

    return (
        <>
            <Card className="mb-2">
                <CardHeader>
                    <CardTitle>
                        <H3>Create Maintenance Category</H3>
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="grid grid-cols-2">
                        <div className="flex flex-col gap-4">
                            <Label label="Category name">
                                <Input
                                    id={`category-name`}
                                    type="text"
                                    placeholder="Category name"
                                />
                            </Label>
                            <Label label="Abbreviation">
                                <Input
                                    id={`category-abbr`}
                                    type="text"
                                    placeholder="Abbreviation"
                                />
                            </Label>
                        </div>
                    </div>
                </CardContent>
            </Card>

            <FooterWrapper>
                <Button
                    size="sm"
                    variant="back"
                    // link="/settings/maintenance/category"
                    onClick={() =>
                        router.push('/settings/maintenance/category')
                    }>
                    Back
                </Button>
                <Button size="sm" onClick={handleCreate}>
                    Create category
                </Button>
            </FooterWrapper>
        </>
    )
}
