'use client'
import React from 'react'
import { useMutation } from '@apollo/client'
import { useState } from 'react'
import {
    UPDATE_MAINTENANCE_CATEGORY,
    DELETE_MAINTENANCE_CATEGORY,
} from '@/app/lib/graphQL/mutation'
import {
    GET_MAINTENANCE_CATEGORY,
    GetMaintenanceCategories,
} from '@/app/lib/graphQL/query'
import { useRouter } from 'next/navigation'
import { getMaintenanceCategoryByID } from '@/app/lib/actions'
import { Input } from '@/components/ui/input'
import { FooterWrapper } from '@/components/footer-wrapper'
import SeaLogsButton from '@/components/ui/sea-logs-button'
import {
    AlertDialogNew,
    Button,
    Card,
    CardContent,
    CardHeader,
    CardTitle,
    H1,
    H2,
    H3,
    Label,
} from '@/components/ui'

export default function EditMaintenanceCategory({
    categoryID,
}: {
    categoryID: number
}) {
    const router = useRouter()
    const [openConfirm, setOpenConfirm] = useState(false)
    const [category, setCategory] = useState<any>()

    getMaintenanceCategoryByID(categoryID, setCategory)

    const handleUpdate = async () => {
        const categoryName = (
            document.getElementById('category-name') as HTMLInputElement
        )?.value
        const categoryAbbr = (
            document.getElementById('category-abbr') as HTMLInputElement
        )?.value
        const variables = {
            input: {
                id: +categoryID,
                name: categoryName,
                abbreviation: categoryAbbr,
            },
        }
        if (categoryName !== '') {
            return await mutationupdateMaintenanceCategory({
                variables,
            })
        }
    }

    const [
        mutationupdateMaintenanceCategory,
        { loading: mutationupdateMaintenanceCategoryLoading },
    ] = useMutation(UPDATE_MAINTENANCE_CATEGORY, {
        refetchQueries: () => [
            {
                query: GET_MAINTENANCE_CATEGORY,
                variables: {
                    clientID: +(localStorage.getItem('clientId') ?? 0),
                },
            },
            {
                query: GetMaintenanceCategories,
                variables: {
                    clientID: +(localStorage.getItem('clientId') ?? 0),
                },
            },
        ],
        awaitRefetchQueries: true,
        onCompleted: (response: any) => {
            const data = response.updateMaintenanceCategory
            if (data.id > 0) {
                router.back()
            } else {
                console.error(
                    'mutationupdateMaintenanceCategory error',
                    response,
                )
            }
        },
        onError: (error: any) => {
            console.error('mutationupdateMaintenanceCategory error', error)
        },
    })

    const handleDeleteCategory = async () => {
        await mutationDeleteMaintenanceCategory({
            variables: {
                ids: [+categoryID],
            },
        })
    }

    const [
        mutationDeleteMaintenanceCategory,
        { loading: mutationDeleteMaintenanceCategoryLoading },
    ] = useMutation(DELETE_MAINTENANCE_CATEGORY, {
        refetchQueries: () => [
            {
                query: GET_MAINTENANCE_CATEGORY,
                variables: {
                    clientID: +(localStorage.getItem('clientId') ?? 0),
                },
            },
            {
                query: GetMaintenanceCategories,
                variables: {
                    clientID: +(localStorage.getItem('clientId') ?? 0),
                },
            },
        ],
        awaitRefetchQueries: true,
        onCompleted: (response: any) => {
            const data = response.deleteMaintenanceCategories
            if (data) {
                router.back()
            } else {
                console.error(
                    'mutationDeleteMaintenanceCategory error',
                    response,
                )
            }
        },
        onError: (error: any) => {
            console.error('mutationDeleteMaintenanceCategory error', error)
        },
    })

    return (
        <>
            <Card className="mb-2">
                <CardHeader>
                    <CardTitle>
                        <H3>Edit Maintenance Category</H3>
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="grid grid-cols-2">
                        <div className="flex flex-col gap-4">
                            <Label label="Category name">
                                <Input
                                    id={`category-name`}
                                    type="text"
                                    placeholder="Category name"
                                    defaultValue={category?.name}
                                />
                            </Label>
                            <Label label="Abbreviation">
                                <Input
                                    id={`category-abbr`}
                                    type="text"
                                    placeholder="Abbreviation"
                                    defaultValue={category?.abbreviation}
                                />
                            </Label>
                        </div>
                    </div>
                </CardContent>
            </Card>
            <FooterWrapper>
                <Button
                    variant="back"
                    size="sm"
                    // link="/settings/maintenance/category"
                    onClick={() =>
                        router.push('/settings/maintenance/category')
                    }>
                    Back
                </Button>
                <Button
                    variant="destructive"
                    size="sm"
                    color="rose"
                    onClick={() => setOpenConfirm(true)}>
                    Delete
                </Button>
                <Button size="sm" onClick={handleUpdate}>
                    Update Category
                </Button>
            </FooterWrapper>
            <AlertDialogNew
                openDialog={openConfirm}
                setOpenDialog={setOpenConfirm}
                handleCreate={handleDeleteCategory}
                actionText="Delete Category"
                title="Delete Category"
                variant="warning">
                Are you sure you want to delete {category?.name}?
            </AlertDialogNew>
        </>
    )
}
