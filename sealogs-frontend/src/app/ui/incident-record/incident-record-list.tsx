'use client'

import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { useLazyQuery } from '@apollo/client'
import { GET_INCIDENT_RECORDS } from './graphql/queries'
import dayjs from 'dayjs'
import { formatDate } from '@/app/helpers/dateHelper'
import Link from 'next/link'
import { DataTable, createColumns } from '@/components/filteredTable'
import { DataTableSortHeader } from '@/components/data-table-sort-header'
import { ListHeader } from '@/components/ui/list-header'
import { Avatar, AvatarFallback, getCrewInitials } from '@/components/ui/avatar'
import { VesselLocationDisplay } from '@/components/ui/vessel-location-display'
import { CrewMemberAvatars } from '@/components/ui/crew-member-avatars'
import { SealogsIncidentIcon } from '@/app/lib/icons/SealogsIncidentIcon'
import IncidentChartsDashboard from './components/incident-charts-dashboard'
import { useVesselIconData } from '@/app/lib/vessel-icon-helper'
import {
    P,
    Separator,
    Tooltip,
    TooltipContent,
    TooltipTrigger,
} from '@/components/ui'
import { SealogsCogIcon } from '@/app/lib/icons'
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { useSidebar } from '@/components/ui/sidebar'

interface SearchFilter {
    vessel?: any
    dateRange?: {
        startDate: string | null
        endDate: string | null
    }
}

// Helper functions

// Format names for display
const formatName = (person: any) => {
    // Return dash if person doesn't exist, or if id is zero
    return person && +person.id !== 0
        ? `${person.firstName} ${person.surname}`
        : ''
}

const IncidentRecordList = () => {
    const router = useRouter()
    const [isLoading, setIsLoading] = useState(true)
    const [incidentRecords, setIncidentRecords] = useState([])

    const [filter, setFilter] = useState<SearchFilter>({})
    const { isMobile } = useSidebar()

    // Get vessel icon data for proper vessel icon display
    const { getVesselWithIcon } = useVesselIconData()

    // GraphQL query to fetch incident records
    const [getIncidentRecords, { loading: queryIncidentRecordsLoading }] =
        useLazyQuery(GET_INCIDENT_RECORDS, {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response) => {
                if (response && response.readIncidentRecords) {
                    setIncidentRecords(response.readIncidentRecords.nodes)
                    setIsLoading(false)
                }
            },
            onError: (error) => {
                console.error('Error fetching incident records:', error)
                setIsLoading(false)
            },
        })

    // Load incident records on initial render and when filter changes
    useEffect(() => {
        loadIncidentRecords()
    }, [filter])

    // Function to load incident records with filters
    const loadIncidentRecords = () => {
        const variables: any = {
            limit: 1000, // Fetch more records for client-side pagination
        }

        // Add filters if they exist
        if (Object.keys(filter).length > 0) {
            const graphqlFilter: any = {}

            // Add vessel filter
            if (filter.vessel && filter.vessel.value) {
                graphqlFilter.vesselID = { eq: filter.vessel.value }
            }

            // Add date range filter
            if (filter.dateRange) {
                if (filter.dateRange.startDate) {
                    graphqlFilter.startDate = {
                        gte: dayjs(filter.dateRange.startDate)
                            .startOf('day')
                            .toISOString(),
                    }
                }
                if (filter.dateRange.endDate) {
                    graphqlFilter.endDate = {
                        lte: dayjs(filter.dateRange.endDate)
                            .endOf('day')
                            .toISOString(),
                    }
                }
            }

            variables.filter = graphqlFilter
        }

        getIncidentRecords({ variables })
    }

    // Handle filter changes
    const handleFilterChange = ({ type, data }: any) => {
        setFilter((prevFilter) => ({
            ...prevFilter,
            [type]: data,
        }))
    }

    // Create column definitions for the DataTable
    const columns = createColumns([
        {
            accessorKey: 'title',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Title" />
            ),
            cellClassName: 'w-full tablet-md:w-auto',
            cell: ({ row }: { row: any }) => {
                const incident = row.original
                return (
                    <div className="w-full space-y-2 my-2.5">
                        <div>
                            <Link
                                href={`/incident-records/edit/?id=${incident.id}`}
                                className="hover:text-curious-blue-400">
                                {incident.title || '-'}
                            </Link>
                            <div className="desktop:hidden mt-2">
                                {incident.membersToNotify?.nodes?.length >
                                    0 && (
                                    <CrewMemberAvatars
                                        members={incident.membersToNotify.nodes}
                                        maxVisible={6}
                                        avatarSize="sm"
                                        mobileClickable={true}
                                    />
                                )}
                            </div>
                        </div>
                        <div className="flex items-center justify-between gap-4">
                            <p className="text-sm laptop:hidden">
                                {formatDate(incident.startDate)} -{' '}
                                {formatDate(incident.endDate)}
                            </p>
                            {/* Mobile: Show vessel and reported by */}
                            <div className="flex items-center gap-2.5">
                                {incident.vessel.id != 0 && (
                                    <>
                                        <div className="tablet-md:hidden">
                                            <VesselLocationDisplay
                                                vessel={getVesselWithIcon(
                                                    incident.vessel.id,
                                                    incident.vessel,
                                                )}
                                                vesselId={incident.vessel.id}
                                                displayText={false}
                                            />
                                        </div>
                                        <Separator
                                            orientation="vertical"
                                            className="h-4 tablet-md:hidden"
                                        />
                                    </>
                                )}

                                {incident.reportedBy.id != 0 && (
                                    <Tooltip mobileClickable>
                                        <TooltipTrigger
                                            mobileClickable
                                            className="tablet-md:hidden">
                                            <Avatar className="h-8 w-8">
                                                <AvatarFallback className="text-xs">
                                                    {getCrewInitials(
                                                        incident.reportedBy
                                                            .firstName,
                                                        incident.reportedBy
                                                            .surname,
                                                    )}
                                                </AvatarFallback>
                                            </Avatar>
                                        </TooltipTrigger>
                                        <TooltipContent className="lg:hidden">
                                            {formatName(incident.reportedBy)}
                                        </TooltipContent>
                                    </Tooltip>
                                )}
                            </div>
                        </div>
                    </div>
                )
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA = rowA?.original?.title || ''
                const valueB = rowB?.original?.title || ''
                return valueA.localeCompare(valueB)
            },
        },
        {
            accessorKey: 'vessel',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Vessel" />
            ),
            cellAlignment: 'left' as const,
            breakpoint: 'tablet-md' as const,
            cellClassName: 'px-2.5',
            cell: ({ row }: { row: any }) => {
                const incident = row.original
                return (
                    <>
                        {incident.vessel.id != 0 ? (
                            <VesselLocationDisplay
                                vessel={getVesselWithIcon(
                                    incident.vessel.id,
                                    incident.vessel,
                                )}
                                vesselId={incident.vessel.id}
                                displayText={incident.vessel.title || ''}
                            />
                        ) : (
                            <P>-</P>
                        )}
                    </>
                )
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA = rowA?.original?.vessel?.title || ''
                const valueB = rowB?.original?.vessel?.title || ''
                return valueA.localeCompare(valueB)
            },
        },
        {
            accessorKey: 'reportedBy',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Reported By" />
            ),
            breakpoint: 'tablet-md' as const,
            cellClassName: 'px-2.5',
            cellAlignment: 'left' as const,
            cell: ({ row }: { row: any }) => {
                const incident = row.original
                return (
                    <>
                        {incident.reportedBy && incident.reportedBy.id > 0 && (
                            <div className="flex items-center gap-2.5">
                                <Tooltip mobileClickable>
                                    <TooltipTrigger mobileClickable>
                                        <Avatar className="h-8 w-8">
                                            <AvatarFallback className="text-xs">
                                                {getCrewInitials(
                                                    incident.reportedBy
                                                        .firstName,
                                                    incident.reportedBy.surname,
                                                )}
                                            </AvatarFallback>
                                        </Avatar>
                                    </TooltipTrigger>
                                    <TooltipContent className="lg:hidden">
                                        {formatName(incident.reportedBy)}
                                    </TooltipContent>
                                </Tooltip>
                                <span className="hidden small:block">
                                    {formatName(incident.reportedBy)}
                                </span>
                            </div>
                        )}
                    </>
                )
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA = formatName(rowA?.original?.reportedBy) || ''
                const valueB = formatName(rowB?.original?.reportedBy) || ''
                return valueA.localeCompare(valueB)
            },
        },
        {
            accessorKey: 'membersToNotify',
            header: 'Members to Notify',
            cellAlignment: 'left' as const,
            breakpoint: 'desktop' as const,
            cellClassName: 'px-2.5',
            cell: ({ row }: { row: any }) => {
                const incident = row.original
                const members = incident.membersToNotify?.nodes || []

                if (members.length === 0) {
                    return <span className="text-muted-foreground">-</span>
                }

                return (
                    <CrewMemberAvatars
                        members={members}
                        maxVisible={3}
                        avatarSize="sm"
                        mobileClickable={true}
                    />
                )
            },
        },
        {
            accessorKey: 'startDate',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Start Date" />
            ),
            cellAlignment: 'left' as const,
            breakpoint: 'laptop' as const,
            cellClassName: 'px-2.5',
            cell: ({ row }: { row: any }) => {
                const incident = row.original
                return <>{formatDate(incident.startDate)}</>
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA = rowA?.original?.startDate || ''
                const valueB = rowB?.original?.startDate || ''
                return valueA.localeCompare(valueB)
            },
        },
        {
            accessorKey: 'endDate',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="End Date" />
            ),
            cellAlignment: 'right' as const,
            breakpoint: 'laptop' as const,
            cellClassName: 'px-2.5',
            cell: ({ row }: { row: any }) => {
                const incident = row.original
                return <>{formatDate(incident.endDate)}</>
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA = rowA?.original?.endDate || ''
                const valueB = rowB?.original?.endDate || ''
                return valueA.localeCompare(valueB)
            },
        },
    ])

    return (
        <div className="w-full py-0">
            <ListHeader
                icon={<SealogsIncidentIcon className="w-8 h-8" />}
                title="Incident Records"
                actions={
                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <SealogsCogIcon size={36} />
                        </DropdownMenuTrigger>
                        <DropdownMenuContent
                            side={isMobile ? 'bottom' : 'right'}
                            align={isMobile ? 'end' : 'start'}>
                            <DropdownMenuItem
                                onClick={() => {
                                    router.push('/incident-records/create')
                                }}>
                                New incident record
                            </DropdownMenuItem>
                        </DropdownMenuContent>
                    </DropdownMenu>
                }
            />

            {/* Chart Dashboard */}
            <IncidentChartsDashboard />

            {/* Table with integrated filters */}
            <DataTable
                columns={columns}
                data={incidentRecords}
                noDataText="No incident records found"
                showToolbar={true}
                isLoading={isLoading || queryIncidentRecordsLoading}
                pageSize={10}
                onChange={handleFilterChange}
            />
        </div>
    )
}
export default IncidentRecordList
